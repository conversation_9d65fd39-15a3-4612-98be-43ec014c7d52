@echo off
chcp 65001 > nul
title 富途-Quantower橋接器 - 簡化運行

echo.
echo ===============================================
echo   富途-Quantower橋接器 - 簡化運行
echo ===============================================
echo.

:: 檢查Java環境
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 未找到Java環境
    echo.
    echo 請先安裝Java JDK 11或更高版本：
    echo 1. 訪問 https://adoptium.net/
    echo 2. 下載並安裝Eclipse Temurin JDK 11
    echo 3. 重啟命令提示符後再運行此腳本
    echo.
    pause
    exit /b 1
)

:: 檢查富途SDK
if not exist "lib\futu-api.jar" (
    echo [錯誤] 未找到富途SDK
    echo.
    echo 請按以下步驟安裝富途SDK：
    echo 1. 訪問 https://openapi.futunn.com/
    echo 2. 註冊並下載Java SDK
    echo 3. 將jar文件重命名為 futu-api.jar
    echo 4. 放置到 lib\ 目錄下
    echo.
    pause
    exit /b 1
)

:: 創建必要目錄
if not exist "build" mkdir build
if not exist "logs" mkdir logs

echo [信息] 正在編譯項目...

:: 設置classpath
set CLASSPATH=lib\futu-api.jar

:: 編譯Java文件
javac -cp "%CLASSPATH%" -d build -sourcepath src\main\java src\main\java\com\niuniu\bridge\*.java src\main\java\com\niuniu\bridge\config\*.java src\main\java\com\niuniu\bridge\ib\*.java src\main\java\com\niuniu\bridge\futu\*.java src\main\java\com\niuniu\bridge\data\*.java 2>compile-errors.txt

if %errorlevel% neq 0 (
    echo [錯誤] 編譯失敗，請檢查 compile-errors.txt 文件
    type compile-errors.txt
    pause
    exit /b 1
)

echo [信息] 編譯成功！

:: 複製資源文件
if exist "src\main\resources" (
    xcopy /s /y "src\main\resources\*" "build\" > nul
)

echo [信息] 正在啟動橋接器...
echo [信息] 請確保富途牛牛客戶端已啟動並開啟OpenAPI
echo [信息] 橋接器將監聽端口7497，等待Quantower連接
echo.

:: 運行程序
java -cp "build;%CLASSPATH%" com.niuniu.bridge.FutuQuantowerBridge

echo.
echo [信息] 橋接器已停止
pause
