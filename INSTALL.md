# 富途-Quantower橋接器安裝指南

## 環境要求

### 必需軟件

1. **Java Development Kit (JDK) 11+**
2. **Apache Maven 3.6+**
3. **富途牛牛客戶端**
4. **Quantower交易平台**

## 詳細安裝步驟

### 1. 安裝Java JDK

#### 方法一：使用Oracle JDK
1. 訪問：https://www.oracle.com/java/technologies/downloads/
2. 下載Java 11或更高版本
3. 運行安裝程序
4. 設置環境變量：
   ```
   JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
   PATH=%JAVA_HOME%\bin;%PATH%
   ```

#### 方法二：使用OpenJDK（推薦）
1. 訪問：https://adoptium.net/
2. 下載Eclipse Temurin JDK 11
3. 運行安裝程序（會自動設置環境變量）

#### 驗證安裝
```bash
java -version
javac -version
```

### 2. 安裝Apache Maven

#### 下載和安裝
1. 訪問：https://maven.apache.org/download.cgi
2. 下載Binary zip archive
3. 解壓到：`C:\Program Files\Apache\maven`
4. 設置環境變量：
   ```
   MAVEN_HOME=C:\Program Files\Apache\maven
   PATH=%MAVEN_HOME%\bin;%PATH%
   ```

#### 驗證安裝
```bash
mvn -version
```

### 3. 安裝富途牛牛

1. 訪問：https://www.futunn.com/
2. 下載並安裝富途牛牛客戶端
3. 註冊賬號並開通OpenAPI權限
4. 在設置中啟用OpenAPI功能

### 4. 安裝Quantower

1. 訪問：https://www.quantower.com/
2. 下載並安裝Quantower平台
3. 註冊賬號（免費版即可）

## 快速安裝腳本

### Windows一鍵安裝（需要管理員權限）

創建 `quick-install.bat`：

```batch
@echo off
echo 正在安裝Java和Maven...

:: 使用Chocolatey安裝（如果已安裝）
choco install openjdk11 maven -y

:: 或者使用winget安裝
winget install Microsoft.OpenJDK.11
winget install Apache.Maven

echo 安裝完成，請重啟命令提示符
pause
```

## 富途SDK安裝

### 下載富途SDK

1. 登錄富途開發者中心：https://openapi.futunn.com/
2. 下載Java SDK
3. 將jar文件重命名為 `futu-api.jar`
4. 放置到項目的 `lib/` 目錄下

### 手動安裝到Maven倉庫

```bash
mvn install:install-file \
  -Dfile=lib/futu-api.jar \
  -DgroupId=com.futu \
  -DartifactId=futu-api \
  -Dversion=6.0.1308 \
  -Dpackaging=jar
```

## 驗證安裝

運行以下命令驗證所有組件：

```bash
# 檢查Java
java -version

# 檢查Maven
mvn -version

# 編譯項目
mvn clean compile

# 運行測試
mvn test
```

## 故障排除

### 常見問題

1. **"mvn不是內部或外部命令"**
   - 檢查Maven是否正確安裝
   - 檢查PATH環境變量是否包含Maven的bin目錄

2. **"java不是內部或外部命令"**
   - 檢查JDK是否正確安裝
   - 檢查JAVA_HOME和PATH環境變量

3. **編譯失敗：找不到富途API**
   - 確保futu-api.jar在lib目錄下
   - 檢查jar文件名是否正確

4. **富途API連接失敗**
   - 確保富途牛牛客戶端已啟動
   - 檢查OpenAPI功能是否已啟用
   - 確認端口11111未被占用

### 環境變量設置

#### Windows系統
1. 右鍵"此電腦" → "屬性"
2. 點擊"高級系統設置"
3. 點擊"環境變量"
4. 在"系統變量"中添加：
   ```
   JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
   MAVEN_HOME=C:\Program Files\Apache\maven
   ```
5. 編輯PATH變量，添加：
   ```
   %JAVA_HOME%\bin
   %MAVEN_HOME%\bin
   ```

## 下一步

安裝完成後，請參考 [README.md](README.md) 進行配置和運行。
