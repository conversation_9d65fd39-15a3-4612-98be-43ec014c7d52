package com.niuniu.bridge;

import com.niuniu.bridge.ib.IBMessage;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * IB協議測試類
 */
public class IBProtocolTest {
    
    @Test
    public void testIBMessageCreation() {
        // 測試創建IB消息
        IBMessage message = new IBMessage("1", "2", "AAPL", "STK", "SMART", "USD");
        
        assertEquals(6, message.getFieldCount());
        assertEquals("1", message.getField(0));
        assertEquals("AAPL", message.getField(2));
        assertEquals(1, message.getMessageId());
    }
    
    @Test
    public void testHandshakeMessage() {
        // 測試握手消息識別
        IBMessage handshake = new IBMessage("API");
        assertTrue(handshake.isHandshakeMessage());
        
        IBMessage normal = new IBMessage("1", "2", "test");
        assertFalse(normal.isHandshakeMessage());
    }
    
    @Test
    public void testVersionMessage() {
        // 測試版本消息識別
        IBMessage version = new IBMessage("v157..178");
        assertTrue(version.isVersionMessage());
        
        IBMessage normal = new IBMessage("1", "2", "test");
        assertFalse(normal.isVersionMessage());
    }
    
    @Test
    public void testFieldConversion() {
        // 測試字段類型轉換
        IBMessage message = new IBMessage("1", "123", "45.67", "invalid");
        
        assertEquals(1, message.getFieldAsInt(0));
        assertEquals(123, message.getFieldAsInt(1));
        assertEquals(45.67, message.getFieldAsDouble(2), 0.001);
        assertEquals(0, message.getFieldAsInt(3)); // 無效數字應返回0
        assertEquals(0.0, message.getFieldAsDouble(3), 0.001); // 無效數字應返回0.0
    }
    
    @Test
    public void testEmptyFields() {
        // 測試空字段處理
        IBMessage message = new IBMessage();
        
        assertEquals(0, message.getFieldCount());
        assertEquals("", message.getField(0));
        assertEquals(0, message.getMessageId());
    }
}
