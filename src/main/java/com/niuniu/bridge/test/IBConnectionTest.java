package com.niuniu.bridge.test;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * IB連接測試工具
 * 
 * 用於測試橋接器的IB協議實現是否正確
 */
public class IBConnectionTest {
    
    private static final String HOST = "127.0.0.1";
    private static final int PORT = 7497;
    
    public static void main(String[] args) {
        IBConnectionTest test = new IBConnectionTest();
        test.testConnection();
    }
    
    public void testConnection() {
        try (Socket socket = new Socket(HOST, PORT)) {
            System.out.println("已連接到橋接器: " + HOST + ":" + PORT);
            
            DataOutputStream out = new DataOutputStream(socket.getOutputStream());
            DataInputStream in = new DataInputStream(socket.getInputStream());
            
            // 1. 發送握手消息
            System.out.println("發送握手消息...");
            sendMessage(out, "API");
            
            // 讀取響應
            String response = readMessage(in);
            System.out.println("收到響應: " + response);
            
            // 2. 發送版本協商
            System.out.println("發送版本協商...");
            sendMessage(out, "v100..178");
            
            // 讀取響應
            response = readMessage(in);
            System.out.println("收到響應: " + response);
            
            // 3. 發送startApi
            System.out.println("發送startApi...");
            sendMessage(out, "71", "2", "1", "");
            
            // 讀取響應
            response = readMessage(in);
            System.out.println("收到響應: " + response);
            
            // 4. 請求市場數據
            System.out.println("請求市場數據...");
            sendMessage(out, "1", "11", "1", "0", "AAPL", "STK", "", "0.0", "", "SMART", "USD", "", "", "");
            
            // 持續讀取數據
            System.out.println("等待市場數據...");
            for (int i = 0; i < 10; i++) {
                try {
                    response = readMessage(in);
                    System.out.println("收到數據: " + response);
                } catch (Exception e) {
                    System.out.println("讀取超時或連接關閉");
                    break;
                }
            }
            
        } catch (Exception e) {
            System.err.println("連接測試失敗: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void sendMessage(DataOutputStream out, String... fields) throws IOException {
        StringBuilder message = new StringBuilder();
        for (int i = 0; i < fields.length; i++) {
            message.append(fields[i]);
            if (i < fields.length - 1) {
                message.append('\0');
            }
        }
        
        byte[] messageBytes = message.toString().getBytes(StandardCharsets.UTF_8);
        out.writeInt(messageBytes.length);
        out.write(messageBytes);
        out.flush();
        
        System.out.println("發送: " + String.join(",", fields));
    }
    
    private String readMessage(DataInputStream in) throws IOException {
        // 設置讀取超時
        int length = in.readInt();
        byte[] messageBytes = new byte[length];
        in.readFully(messageBytes);
        
        String message = new String(messageBytes, StandardCharsets.UTF_8);
        return message.replace('\0', ',');
    }
}
