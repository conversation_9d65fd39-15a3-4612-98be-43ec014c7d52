package com.niuniu.bridge.data;

import com.futu.openapi.pb.QotCommon;
import com.niuniu.bridge.ib.IBClientSession;
import com.niuniu.bridge.ib.IBGatewaySimulator;
import com.niuniu.bridge.ib.IBMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 數據轉發器
 * 
 * 負責將富途API的數據轉換為IB格式並轉發給Quantower客戶端
 */
public class DataForwarder {
    
    private static final Logger logger = LoggerFactory.getLogger(DataForwarder.class);
    
    private final IBGatewaySimulator simulator;
    private final ScheduledExecutorService scheduler;
    
    // 訂閱映射：reqId -> SubscriptionInfo
    private final ConcurrentHashMap<Integer, SubscriptionInfo> subscriptions = new ConcurrentHashMap<>();
    
    // 股票代碼映射：富途代碼 -> IB代碼
    private final ConcurrentHashMap<String, String> symbolMapping = new ConcurrentHashMap<>();
    
    public DataForwarder(IBGatewaySimulator simulator) {
        this.simulator = simulator;
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // 啟動心跳任務
        startHeartbeat();
    }
    
    /**
     * 添加市場數據訂閱
     */
    public void addMarketDataSubscription(int reqId, String symbol, String secType, 
                                        String exchange, String currency, IBClientSession session) {
        
        // 轉換股票代碼格式
        String futuSymbol = convertToFutuSymbol(symbol, exchange);
        String futuMarket = convertToFutuMarket(exchange);
        
        SubscriptionInfo info = new SubscriptionInfo(reqId, symbol, futuSymbol, 
            secType, exchange, currency, futuMarket, session);
        
        subscriptions.put(reqId, info);
        symbolMapping.put(futuSymbol, symbol);
        
        logger.info("添加市場數據訂閱: reqId={}, symbol={}, futuSymbol={}, market={}", 
            reqId, symbol, futuSymbol, futuMarket);
        
        // 向富途API訂閱數據
        try {
            // TODO: 調用富途API訂閱
            // futuClient.subscribeQuote(futuSymbol, futuMarket);
            
            // 發送訂閱確認
            sendSubscriptionConfirmation(session, reqId, symbol);
            
        } catch (Exception e) {
            logger.error("訂閱富途數據失敗: symbol={}", futuSymbol, e);
            sendError(session, reqId, 200, "訂閱失敗: " + e.getMessage());
        }
    }
    
    /**
     * 移除市場數據訂閱
     */
    public void removeMarketDataSubscription(int reqId) {
        SubscriptionInfo info = subscriptions.remove(reqId);
        if (info != null) {
            symbolMapping.remove(info.futuSymbol);
            
            logger.info("移除市場數據訂閱: reqId={}, symbol={}", reqId, info.symbol);
            
            // TODO: 取消富途API訂閱
            // futuClient.unsubscribeQuote(info.futuSymbol);
        }
    }
    
    /**
     * 處理富途基本報價數據
     */
    public void onFutuBasicQuote(QotCommon.BasicQot basicQot) {
        QotCommon.Security security = basicQot.getSecurity();
        String futuSymbol = security.getCode();
        String ibSymbol = symbolMapping.get(futuSymbol);
        
        if (ibSymbol == null) {
            return; // 未訂閱的股票
        }
        
        // 查找所有訂閱該股票的請求
        subscriptions.values().stream()
            .filter(sub -> sub.futuSymbol.equals(futuSymbol))
            .forEach(sub -> forwardBasicQuote(sub, basicQot));
    }
    
    /**
     * 轉發基本報價數據
     */
    private void forwardBasicQuote(SubscriptionInfo sub, QotCommon.BasicQot basicQot) {
        try {
            // 提取價格數據
            double curPrice = basicQot.getCurPrice();
            double openPrice = basicQot.getOpenPrice();
            double highPrice = basicQot.getHighPrice();
            double lowPrice = basicQot.getLowPrice();
            double preClosePrice = basicQot.getPreClosePrice();
            long volume = basicQot.getVolume();
            double turnover = basicQot.getTurnover();
            
            // 計算bid/ask價格（模擬）
            double spread = curPrice * 0.001; // 0.1%的價差
            double bidPrice = curPrice - spread / 2;
            double askPrice = curPrice + spread / 2;
            
            // 發送tick數據
            sendTickPrice(sub.session, sub.reqId, 1, bidPrice, 100);    // BID
            sendTickPrice(sub.session, sub.reqId, 2, askPrice, 100);    // ASK
            sendTickPrice(sub.session, sub.reqId, 4, curPrice, (int)Math.min(volume, Integer.MAX_VALUE)); // LAST
            sendTickPrice(sub.session, sub.reqId, 6, highPrice, 0);     // HIGH
            sendTickPrice(sub.session, sub.reqId, 7, lowPrice, 0);      // LOW
            sendTickPrice(sub.session, sub.reqId, 9, preClosePrice, 0); // CLOSE
            
            // 發送成交量
            sendTickSize(sub.session, sub.reqId, 8, (int)Math.min(volume, Integer.MAX_VALUE)); // VOLUME
            
            logger.debug("轉發報價數據: symbol={}, price={}, volume={}", 
                sub.symbol, curPrice, volume);
                
        } catch (Exception e) {
            logger.error("轉發報價數據失敗: symbol={}", sub.symbol, e);
        }
    }
    
    /**
     * 發送tick價格數據
     */
    private void sendTickPrice(IBClientSession session, int reqId, int tickType, 
                              double price, int size) {
        // IB tick價格消息格式: [1, version, reqId, tickType, price, size, canAutoExecute]
        IBMessage message = new IBMessage("1", "1", String.valueOf(reqId), 
            String.valueOf(tickType), formatPrice(price), String.valueOf(size), "1");
        session.sendMessage(message);
    }
    
    /**
     * 發送tick數量數據
     */
    private void sendTickSize(IBClientSession session, int reqId, int tickType, int size) {
        // IB tick數量消息格式: [2, version, reqId, tickType, size]
        IBMessage message = new IBMessage("2", "1", String.valueOf(reqId), 
            String.valueOf(tickType), String.valueOf(size));
        session.sendMessage(message);
    }
    
    /**
     * 發送訂閱確認
     */
    private void sendSubscriptionConfirmation(IBClientSession session, int reqId, String symbol) {
        // 發送合約詳情 (消息ID: 10)
        IBMessage contractDetails = new IBMessage("10", "8", String.valueOf(reqId), 
            symbol, "STK", "", "0.01", "SMART", "", "", "USD", symbol, "STK");
        session.sendMessage(contractDetails);
        
        // 發送合約詳情結束 (消息ID: 52)
        IBMessage contractDetailsEnd = new IBMessage("52", "1", String.valueOf(reqId));
        session.sendMessage(contractDetailsEnd);
    }
    
    /**
     * 發送錯誤消息
     */
    private void sendError(IBClientSession session, int reqId, int errorCode, String errorMsg) {
        IBMessage error = new IBMessage("4", "2", String.valueOf(reqId), 
            String.valueOf(errorCode), errorMsg);
        session.sendMessage(error);
    }
    
    /**
     * 轉換為富途股票代碼格式
     */
    private String convertToFutuSymbol(String ibSymbol, String exchange) {
        // 根據交易所轉換股票代碼格式
        switch (exchange.toUpperCase()) {
            case "SEHK":
            case "HKEX":
                // 港股：去掉前綴0，例如 "00700" -> "700"
                return ibSymbol.replaceFirst("^0+", "");
            case "NASDAQ":
            case "NYSE":
            case "SMART":
                // 美股：直接使用
                return ibSymbol;
            case "SSE":
            case "SZSE":
                // A股：保持原格式
                return ibSymbol;
            default:
                return ibSymbol;
        }
    }
    
    /**
     * 轉換為富途市場代碼
     */
    private String convertToFutuMarket(String exchange) {
        switch (exchange.toUpperCase()) {
            case "SEHK":
            case "HKEX":
                return "HK";
            case "NASDAQ":
            case "NYSE":
            case "SMART":
                return "US";
            case "SSE":
                return "SH";
            case "SZSE":
                return "SZ";
            default:
                return "HK"; // 默認港股
        }
    }
    
    /**
     * 格式化價格
     */
    private String formatPrice(double price) {
        return String.format("%.4f", price);
    }
    
    /**
     * 啟動心跳任務
     */
    private void startHeartbeat() {
        scheduler.scheduleAtFixedRate(() -> {
            // 定期清理無效訂閱
            subscriptions.entrySet().removeIf(entry -> {
                IBClientSession session = entry.getValue().session;
                return !session.isActive();
            });
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 關閉轉發器
     */
    public void shutdown() {
        scheduler.shutdown();
        subscriptions.clear();
        symbolMapping.clear();
        logger.info("數據轉發器已關閉");
    }
    
    /**
     * 訂閱信息
     */
    private static class SubscriptionInfo {
        final int reqId;
        final String symbol;        // IB格式股票代碼
        final String futuSymbol;    // 富途格式股票代碼
        final String secType;
        final String exchange;
        final String currency;
        final String futuMarket;    // 富途市場代碼
        final IBClientSession session;
        
        SubscriptionInfo(int reqId, String symbol, String futuSymbol, String secType, 
                        String exchange, String currency, String futuMarket, IBClientSession session) {
            this.reqId = reqId;
            this.symbol = symbol;
            this.futuSymbol = futuSymbol;
            this.secType = secType;
            this.exchange = exchange;
            this.currency = currency;
            this.futuMarket = futuMarket;
            this.session = session;
        }
    }
}
