package com.niuniu.bridge.data;

import com.futu.openapi.pb.QotCommon;
import com.futu.openapi.pb.QotGetKL;
import com.niuniu.bridge.futu.FutuApiClient;
import com.niuniu.bridge.ib.IBClientSession;
import com.niuniu.bridge.ib.IBMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 歷史數據處理器
 * 
 * 處理來自Quantower的歷史數據請求，從富途API獲取K線數據並轉換為IB格式
 */
public class HistoricalDataHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(HistoricalDataHandler.class);
    
    private final FutuApiClient futuClient;
    private final ConcurrentHashMap<Integer, HistoricalRequest> pendingRequests = new ConcurrentHashMap<>();
    
    public HistoricalDataHandler(FutuApiClient futuClient) {
        this.futuClient = futuClient;
    }
    
    /**
     * 處理歷史數據請求
     */
    public void handleHistoricalDataRequest(IBClientSession session, IBMessage msg) {
        try {
            // 解析IB歷史數據請求
            int reqId = msg.getFieldAsInt(2);
            String symbol = msg.getField(4);
            String secType = msg.getField(5);
            String exchange = msg.getField(8);
            String endDateTime = msg.getField(9);  // 格式: "20231201 23:59:59"
            String durationStr = msg.getField(10); // 格式: "1 D", "1 W", "1 M"
            String barSizeSetting = msg.getField(11); // 格式: "1 min", "5 mins", "1 day"
            String whatToShow = msg.getField(12);   // "TRADES", "MIDPOINT", "BID", "ASK"
            int useRTH = msg.getFieldAsInt(13);     // 1=只使用常規交易時間
            
            logger.info("收到歷史數據請求: reqId={}, symbol={}, duration={}, barSize={}", 
                reqId, symbol, durationStr, barSizeSetting);
            
            // 轉換參數
            String futuSymbol = convertToFutuSymbol(symbol, exchange);
            String futuMarket = convertToFutuMarket(exchange);
            int klType = convertToKLType(barSizeSetting);
            int num = calculateKLNum(durationStr, barSizeSetting);
            
            // 保存請求信息
            HistoricalRequest request = new HistoricalRequest(reqId, symbol, futuSymbol, 
                futuMarket, klType, num, session);
            pendingRequests.put(reqId, request);
            
            // 異步獲取K線數據
            CompletableFuture.runAsync(() -> {
                try {
                    requestKLineData(request);
                } catch (Exception e) {
                    logger.error("獲取K線數據失敗: reqId={}, symbol={}", reqId, symbol, e);
                    sendHistoricalDataError(session, reqId, "獲取歷史數據失敗: " + e.getMessage());
                    pendingRequests.remove(reqId);
                }
            });
            
        } catch (Exception e) {
            logger.error("處理歷史數據請求失敗", e);
            int reqId = msg.getFieldAsInt(2);
            sendHistoricalDataError(session, reqId, "請求參數錯誤: " + e.getMessage());
        }
    }
    
    /**
     * 請求富途K線數據
     */
    private void requestKLineData(HistoricalRequest request) throws Exception {
        if (!futuClient.isConnected()) {
            throw new IllegalStateException("富途API未連接");
        }
        
        // 構建富途K線請求
        QotCommon.Security security = QotCommon.Security.newBuilder()
            .setMarket(getMarketCode(request.futuMarket))
            .setCode(request.futuSymbol)
            .build();
        
        QotGetKL.Request.Builder reqBuilder = QotGetKL.Request.newBuilder();
        reqBuilder.setSecurity(security);
        reqBuilder.setKlType(request.klType);
        reqBuilder.setReqNum(request.num);
        reqBuilder.setEndTime(""); // 空字符串表示最新時間
        
        QotGetKL.Request klRequest = reqBuilder.build();
        
        // 發送請求到富途API
        futuClient.getQotConn().getKL(klRequest);
        
        logger.debug("已發送K線請求到富途API: symbol={}, klType={}, num={}", 
            request.futuSymbol, request.klType, request.num);
    }
    
    /**
     * 處理富途K線響應
     */
    public void onFutuKLResponse(QotGetKL.Response response) {
        if (response.getRetType() != 0) {
            logger.error("富途K線請求失敗: {}", response.getRetMsg());
            return;
        }
        
        try {
            QotCommon.Security security = response.getS2C().getSecurity();
            String futuSymbol = security.getCode();
            
            // 查找對應的請求
            HistoricalRequest request = findRequestBySymbol(futuSymbol);
            if (request == null) {
                logger.warn("未找到對應的歷史數據請求: symbol={}", futuSymbol);
                return;
            }
            
            List<QotCommon.KLine> klList = response.getS2C().getKlListList();
            logger.info("收到K線數據: symbol={}, 數量={}", futuSymbol, klList.size());
            
            // 轉換並發送歷史數據
            sendHistoricalData(request, klList);
            
            // 清理請求
            pendingRequests.remove(request.reqId);
            
        } catch (Exception e) {
            logger.error("處理富途K線響應失敗", e);
        }
    }
    
    /**
     * 發送歷史數據到IB客戶端
     */
    private void sendHistoricalData(HistoricalRequest request, List<QotCommon.KLine> klList) {
        try {
            // 發送歷史數據開始標記
            IBMessage startMsg = new IBMessage("17", "3", String.valueOf(request.reqId), 
                request.symbol + "-STK-SMART-USD", "finished-" + klList.size() + "-" + klList.size());
            request.session.sendMessage(startMsg);
            
            // 發送每個K線數據
            for (QotCommon.KLine kl : klList) {
                String date = formatKLineTime(kl.getTime());
                double open = kl.getOpenPrice();
                double high = kl.getHighPrice();
                double low = kl.getLowPrice();
                double close = kl.getClosePrice();
                long volume = kl.getVolume();
                double wap = (high + low + close) / 3; // 加權平均價格
                int hasGaps = 0;
                int barCount = 1;
                
                IBMessage barMsg = new IBMessage("17", "3", String.valueOf(request.reqId),
                    date, String.format("%.4f", open), String.format("%.4f", high),
                    String.format("%.4f", low), String.format("%.4f", close),
                    String.valueOf(volume), String.format("%.4f", wap), 
                    String.valueOf(hasGaps), String.valueOf(barCount));
                
                request.session.sendMessage(barMsg);
            }
            
            // 發送歷史數據結束標記
            IBMessage endMsg = new IBMessage("17", "3", String.valueOf(request.reqId), 
                "finished", "", "", "", "", "", "", "", "");
            request.session.sendMessage(endMsg);
            
            logger.info("歷史數據發送完成: reqId={}, symbol={}, 數量={}", 
                request.reqId, request.symbol, klList.size());
                
        } catch (Exception e) {
            logger.error("發送歷史數據失敗: reqId={}", request.reqId, e);
            sendHistoricalDataError(request.session, request.reqId, "數據轉換失敗");
        }
    }
    
    /**
     * 發送歷史數據錯誤
     */
    private void sendHistoricalDataError(IBClientSession session, int reqId, String errorMsg) {
        IBMessage error = new IBMessage("4", "2", String.valueOf(reqId), "162", errorMsg);
        session.sendMessage(error);
    }
    
    /**
     * 根據股票代碼查找請求
     */
    private HistoricalRequest findRequestBySymbol(String futuSymbol) {
        return pendingRequests.values().stream()
            .filter(req -> req.futuSymbol.equals(futuSymbol))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 轉換為富途股票代碼
     */
    private String convertToFutuSymbol(String ibSymbol, String exchange) {
        switch (exchange.toUpperCase()) {
            case "SEHK":
            case "HKEX":
                return ibSymbol.replaceFirst("^0+", "");
            default:
                return ibSymbol;
        }
    }
    
    /**
     * 轉換為富途市場代碼
     */
    private String convertToFutuMarket(String exchange) {
        switch (exchange.toUpperCase()) {
            case "SEHK":
            case "HKEX":
                return "HK";
            case "NASDAQ":
            case "NYSE":
            case "SMART":
                return "US";
            case "SSE":
                return "SH";
            case "SZSE":
                return "SZ";
            default:
                return "HK";
        }
    }
    
    /**
     * 轉換為富途K線類型
     */
    private int convertToKLType(String barSizeSetting) {
        switch (barSizeSetting.toLowerCase()) {
            case "1 min":
                return QotCommon.KLType.KLType_1Min_VALUE;
            case "5 mins":
                return QotCommon.KLType.KLType_5Min_VALUE;
            case "15 mins":
                return QotCommon.KLType.KLType_15Min_VALUE;
            case "30 mins":
                return QotCommon.KLType.KLType_30Min_VALUE;
            case "1 hour":
                return QotCommon.KLType.KLType_60Min_VALUE;
            case "1 day":
                return QotCommon.KLType.KLType_Day_VALUE;
            case "1 week":
                return QotCommon.KLType.KLType_Week_VALUE;
            case "1 month":
                return QotCommon.KLType.KLType_Month_VALUE;
            default:
                return QotCommon.KLType.KLType_Day_VALUE;
        }
    }
    
    /**
     * 計算需要的K線數量
     */
    private int calculateKLNum(String durationStr, String barSizeSetting) {
        // 簡化計算，實際應該根據duration和barSize精確計算
        if (durationStr.contains("D")) {
            return 100; // 100個週期
        } else if (durationStr.contains("W")) {
            return 50;
        } else if (durationStr.contains("M")) {
            return 30;
        }
        return 100;
    }
    
    /**
     * 獲取富途市場代碼
     */
    private int getMarketCode(String market) {
        switch (market.toUpperCase()) {
            case "HK":
                return QotCommon.QotMarket.QotMarket_HK_Security_VALUE;
            case "US":
                return QotCommon.QotMarket.QotMarket_US_Security_VALUE;
            case "SH":
                return QotCommon.QotMarket.QotMarket_SH_Security_VALUE;
            case "SZ":
                return QotCommon.QotMarket.QotMarket_SZ_Security_VALUE;
            default:
                return QotCommon.QotMarket.QotMarket_HK_Security_VALUE;
        }
    }
    
    /**
     * 格式化K線時間
     */
    private String formatKLineTime(String futuTime) {
        try {
            // 富途時間格式轉換為IB格式
            // 假設富途時間是Unix時間戳
            long timestamp = Long.parseLong(futuTime);
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
            return dateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"));
        } catch (Exception e) {
            // 如果轉換失敗，返回當前時間
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"));
        }
    }
    
    /**
     * 歷史數據請求信息
     */
    private static class HistoricalRequest {
        final int reqId;
        final String symbol;
        final String futuSymbol;
        final String futuMarket;
        final int klType;
        final int num;
        final IBClientSession session;
        
        HistoricalRequest(int reqId, String symbol, String futuSymbol, String futuMarket,
                         int klType, int num, IBClientSession session) {
            this.reqId = reqId;
            this.symbol = symbol;
            this.futuSymbol = futuSymbol;
            this.futuMarket = futuMarket;
            this.klType = klType;
            this.num = num;
            this.session = session;
        }
    }
}
