package com.niuniu.bridge.futu;

import com.futu.openapi.FTAPI;
import com.futu.openapi.FTAPI_Conn;
import com.futu.openapi.FTAPI_Conn_Qot;
import com.futu.openapi.FTAPI_Conn_Trd;
import com.futu.openapi.pb.QotCommon;
import com.futu.openapi.pb.QotGetBasicQot;
import com.futu.openapi.pb.QotSub;
import com.niuniu.bridge.config.BridgeConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 富途API客戶端
 * 
 * 負責與富途OpenAPI通信，獲取市場數據和執行交易
 */
public class FutuApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(FutuApiClient.class);
    
    private final BridgeConfig.FutuConfig config;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final ConcurrentHashMap<String, QotCommon.Security> subscribedSecurities = new ConcurrentHashMap<>();

    private FTAPI_Conn_Qot qotConn;  // 行情連接
    private FTAPI_Conn_Trd trdConn;  // 交易連接
    private FutuQotHandler qotHandler; // 行情處理器
    
    public FutuApiClient(BridgeConfig.FutuConfig config) {
        this.config = config;
        
        // 初始化富途API
        FTAPI.init();
    }
    
    /**
     * 連接富途API
     */
    public void connect() throws Exception {
        logger.info("正在連接富途OpenAPI: {}:{}", config.getHost(), config.getPort());
        
        try {
            // 創建行情連接
            qotConn = new FTAPI_Conn_Qot();
            qotConn.setClientInfo("FutuQuantowerBridge", 1);
            qotHandler = new FutuQotHandler(this);
            qotConn.setConnSpi(qotHandler);

            // 連接行情服務器
            qotConn.initConnect(config.getHost(), (short) config.getPort(), false);
            
            // 等待連接成功
            Thread.sleep(1000);
            
            // 創建交易連接（如果需要）
            if (config.getUnlockPassword() != null && !config.getUnlockPassword().isEmpty()) {
                trdConn = new FTAPI_Conn_Trd();
                trdConn.setClientInfo("FutuQuantowerBridge", 1);
                trdConn.setConnSpi(new FutuTrdHandler(this));
                trdConn.initConnect(config.getHost(), (short) config.getPort(), false);
                
                Thread.sleep(1000);
            }
            
            connected.set(true);
            logger.info("富途API連接成功");
            
        } catch (Exception e) {
            logger.error("連接富途API失敗", e);
            disconnect();
            throw e;
        }
    }
    
    /**
     * 斷開富途API連接
     */
    public void disconnect() {
        logger.info("正在斷開富途API連接...");
        
        connected.set(false);
        subscribedSecurities.clear();
        
        try {
            if (qotConn != null) {
                qotConn.close();
                qotConn = null;
            }
            
            if (trdConn != null) {
                trdConn.close();
                trdConn = null;
            }
            
            // 清理富途API
            FTAPI.unInit();
            
        } catch (Exception e) {
            logger.error("斷開富途API連接時發生錯誤", e);
        }
        
        logger.info("富途API連接已斷開");
    }
    
    /**
     * 訂閱股票行情
     */
    public void subscribeQuote(String symbol, String market) throws Exception {
        if (!connected.get() || qotConn == null) {
            throw new IllegalStateException("富途API未連接");
        }
        
        // 構建證券對象
        QotCommon.Security security = QotCommon.Security.newBuilder()
            .setMarket(getMarketCode(market))
            .setCode(symbol)
            .build();
        
        // 訂閱基本報價
        QotSub.Request.Builder reqBuilder = QotSub.Request.newBuilder();
        reqBuilder.addSecurityList(security);
        reqBuilder.addSubTypeList(QotCommon.SubType.SubType_Basic_VALUE);  // 基本報價
        reqBuilder.addSubTypeList(QotCommon.SubType.SubType_RT_VALUE);     // 實時報價
        reqBuilder.setIsSubOrUnSub(true);  // 訂閱
        
        QotSub.Request req = reqBuilder.build();
        qotConn.sub(req);
        
        subscribedSecurities.put(symbol, security);
        logger.info("訂閱股票行情: symbol={}, market={}", symbol, market);
    }
    
    /**
     * 取消訂閱股票行情
     */
    public void unsubscribeQuote(String symbol) throws Exception {
        QotCommon.Security security = subscribedSecurities.remove(symbol);
        if (security == null || !connected.get() || qotConn == null) {
            return;
        }
        
        // 取消訂閱
        QotSub.Request.Builder reqBuilder = QotSub.Request.newBuilder();
        reqBuilder.addSecurityList(security);
        reqBuilder.addSubTypeList(QotCommon.SubType.SubType_Basic_VALUE);
        reqBuilder.addSubTypeList(QotCommon.SubType.SubType_RT_VALUE);
        reqBuilder.setIsSubOrUnSub(false);  // 取消訂閱
        
        QotSub.Request req = reqBuilder.build();
        qotConn.sub(req);
        
        logger.info("取消訂閱股票行情: symbol={}", symbol);
    }
    
    /**
     * 獲取基本報價
     */
    public void getBasicQuote(String symbol, String market) throws Exception {
        if (!connected.get() || qotConn == null) {
            throw new IllegalStateException("富途API未連接");
        }
        
        QotCommon.Security security = QotCommon.Security.newBuilder()
            .setMarket(getMarketCode(market))
            .setCode(symbol)
            .build();
        
        QotGetBasicQot.Request.Builder reqBuilder = QotGetBasicQot.Request.newBuilder();
        reqBuilder.addSecurityList(security);
        
        QotGetBasicQot.Request req = reqBuilder.build();
        qotConn.getBasicQot(req);
        
        logger.debug("請求基本報價: symbol={}, market={}", symbol, market);
    }
    
    /**
     * 獲取市場代碼
     */
    private int getMarketCode(String market) {
        if (market == null) {
            return QotCommon.QotMarket.QotMarket_HK_Security_VALUE;  // 默認港股
        }
        
        switch (market.toUpperCase()) {
            case "HK":
            case "SEHK":
                return QotCommon.QotMarket.QotMarket_HK_Security_VALUE;
            case "US":
            case "NASDAQ":
            case "NYSE":
                return QotCommon.QotMarket.QotMarket_US_Security_VALUE;
            case "SH":
            case "SHANGHAI":
                return QotCommon.QotMarket.QotMarket_SH_Security_VALUE;
            case "SZ":
            case "SHENZHEN":
                return QotCommon.QotMarket.QotMarket_SZ_Security_VALUE;
            default:
                return QotCommon.QotMarket.QotMarket_HK_Security_VALUE;
        }
    }
    
    /**
     * 檢查是否已連接
     */
    public boolean isConnected() {
        return connected.get();
    }
    
    /**
     * 獲取行情連接
     */
    public FTAPI_Conn_Qot getQotConn() {
        return qotConn;
    }
    
    /**
     * 獲取交易連接
     */
    public FTAPI_Conn_Trd getTrdConn() {
        return trdConn;
    }
    
    /**
     * 獲取配置
     */
    public BridgeConfig.FutuConfig getConfig() {
        return config;
    }

    /**
     * 設置數據處理器
     */
    public void setDataHandlers(com.niuniu.bridge.data.DataForwarder dataForwarder,
                               com.niuniu.bridge.data.HistoricalDataHandler historicalDataHandler) {
        if (qotHandler != null) {
            qotHandler.setDataForwarder(dataForwarder);
            qotHandler.setHistoricalDataHandler(historicalDataHandler);
        }
    }
}
