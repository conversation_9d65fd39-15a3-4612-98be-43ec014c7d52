package com.niuniu.bridge.futu;

import com.futu.openapi.FTAPI_Conn_Qot;
import com.futu.openapi.pb.QotCommon;
import com.futu.openapi.pb.QotGetBasicQot;
import com.futu.openapi.pb.QotUpdateBasicQot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 富途行情數據處理器
 * 
 * 處理來自富途API的行情數據回調
 */
public class FutuQotHandler implements FTAPI_Conn_Qot.ICallback {
    
    private static final Logger logger = LoggerFactory.getLogger(FutuQotHandler.class);
    
    private final FutuApiClient client;
    
    public FutuQotHandler(FutuApiClient client) {
        this.client = client;
    }
    
    @Override
    public void onInitConnect(FTAPI_Conn_Qot client, long errCode, String desc) {
        if (errCode == 0) {
            logger.info("富途行情連接初始化成功");
        } else {
            logger.error("富途行情連接初始化失敗: errCode={}, desc={}", errCode, desc);
        }
    }
    
    @Override
    public void onDisconnect(FTAPI_Conn_Qot client, long errCode, String desc) {
        logger.warn("富途行情連接斷開: errCode={}, desc={}", errCode, desc);
    }
    
    @Override
    public void onReply_GetBasicQot(FTAPI_Conn_Qot client, int nSerialNo, 
                                   QotGetBasicQot.Response rsp) {
        if (rsp.getRetType() == 0) {
            // 處理基本報價響應
            for (QotCommon.BasicQot basicQot : rsp.getS2C().getBasicQotListList()) {
                processBasicQuote(basicQot);
            }
        } else {
            logger.error("獲取基本報價失敗: retMsg={}", rsp.getRetMsg());
        }
    }
    
    @Override
    public void onPush_UpdateBasicQot(FTAPI_Conn_Qot client, QotUpdateBasicQot.Response rsp) {
        if (rsp.getRetType() == 0) {
            // 處理實時報價推送
            for (QotCommon.BasicQot basicQot : rsp.getS2C().getBasicQotListList()) {
                processBasicQuote(basicQot);
            }
        } else {
            logger.error("實時報價推送錯誤: retMsg={}", rsp.getRetMsg());
        }
    }
    
    /**
     * 處理基本報價數據
     */
    private void processBasicQuote(QotCommon.BasicQot basicQot) {
        QotCommon.Security security = basicQot.getSecurity();
        String symbol = security.getCode();
        int market = security.getMarket();
        
        // 提取報價數據
        double curPrice = basicQot.getCurPrice();      // 當前價格
        double openPrice = basicQot.getOpenPrice();    // 開盤價
        double highPrice = basicQot.getHighPrice();    // 最高價
        double lowPrice = basicQot.getLowPrice();      // 最低價
        double preClosePrice = basicQot.getPreClosePrice(); // 昨收價
        long volume = basicQot.getVolume();            // 成交量
        double turnover = basicQot.getTurnover();      // 成交額
        
        logger.debug("收到報價數據: symbol={}, market={}, curPrice={}, volume={}", 
            symbol, market, curPrice, volume);
        
        // TODO: 將數據轉發給IB客戶端
        // 這裡需要實現數據轉發邏輯
        forwardToIBClients(symbol, market, basicQot);
    }
    
    /**
     * 將富途數據轉發給IB客戶端
     */
    private void forwardToIBClients(String symbol, int market, QotCommon.BasicQot basicQot) {
        // TODO: 實現數據轉發邏輯
        // 需要找到訂閱了該股票的IB客戶端，並發送相應的tick數據
        
        logger.debug("轉發數據到IB客戶端: symbol={}, curPrice={}", 
            symbol, basicQot.getCurPrice());
    }
    
    // 其他回調方法的默認實現
    @Override
    public void onReply_Sub(FTAPI_Conn_Qot client, int nSerialNo, 
                           com.futu.openapi.pb.QotSub.Response rsp) {
        if (rsp.getRetType() == 0) {
            logger.debug("訂閱成功");
        } else {
            logger.error("訂閱失敗: retMsg={}", rsp.getRetMsg());
        }
    }
    
    @Override
    public void onReply_RegQotPush(FTAPI_Conn_Qot client, int nSerialNo, 
                                  com.futu.openapi.pb.QotRegQotPush.Response rsp) {
        if (rsp.getRetType() == 0) {
            logger.debug("註冊推送成功");
        } else {
            logger.error("註冊推送失敗: retMsg={}", rsp.getRetMsg());
        }
    }
    
    @Override
    public void onReply_GetSubInfo(FTAPI_Conn_Qot client, int nSerialNo, 
                                  com.futu.openapi.pb.QotGetSubInfo.Response rsp) {
        // 獲取訂閱信息響應
    }
    
    @Override
    public void onReply_GetTicker(FTAPI_Conn_Qot client, int nSerialNo, 
                                 com.futu.openapi.pb.QotGetTicker.Response rsp) {
        // 獲取逐筆成交響應
    }
    
    @Override
    public void onReply_GetOrderBook(FTAPI_Conn_Qot client, int nSerialNo, 
                                    com.futu.openapi.pb.QotGetOrderBook.Response rsp) {
        // 獲取買賣盤響應
    }
    
    @Override
    public void onReply_GetKL(FTAPI_Conn_Qot client, int nSerialNo, 
                             com.futu.openapi.pb.QotGetKL.Response rsp) {
        // 獲取K線響應
        if (rsp.getRetType() == 0) {
            logger.debug("獲取K線數據成功，數量: {}", 
                rsp.getS2C().getKlListList().size());
        } else {
            logger.error("獲取K線數據失敗: retMsg={}", rsp.getRetMsg());
        }
    }
    
    @Override
    public void onReply_GetRT(FTAPI_Conn_Qot client, int nSerialNo, 
                             com.futu.openapi.pb.QotGetRT.Response rsp) {
        // 獲取分時數據響應
    }
    
    @Override
    public void onReply_GetBroker(FTAPI_Conn_Qot client, int nSerialNo, 
                                 com.futu.openapi.pb.QotGetBroker.Response rsp) {
        // 獲取經紀隊列響應
    }
}
