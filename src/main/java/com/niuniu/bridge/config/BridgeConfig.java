package com.niuniu.bridge.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Map;

/**
 * 橋接器配置管理
 */
public class BridgeConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(BridgeConfig.class);
    
    private FutuConfig futuConfig;
    private IBConfig ibConfig;
    
    public static BridgeConfig load() {
        BridgeConfig config = new BridgeConfig();
        
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = BridgeConfig.class.getClassLoader()
                .getResourceAsStream("application.yml");
            
            if (inputStream == null) {
                logger.warn("未找到配置文件，使用默認配置");
                config.loadDefaults();
            } else {
                Map<String, Object> data = yaml.load(inputStream);
                config.loadFromMap(data);
            }
            
        } catch (Exception e) {
            logger.error("加載配置文件失敗，使用默認配置", e);
            config.loadDefaults();
        }
        
        return config;
    }
    
    @SuppressWarnings("unchecked")
    private void loadFromMap(Map<String, Object> data) {
        // 富途配置
        Map<String, Object> futuData = (Map<String, Object>) data.get("futu");
        if (futuData != null) {
            futuConfig = new FutuConfig();
            futuConfig.setHost((String) futuData.getOrDefault("host", "127.0.0.1"));
            futuConfig.setPort((Integer) futuData.getOrDefault("port", 11111));
            futuConfig.setUnlockPassword((String) futuData.get("unlockPassword"));
            futuConfig.setRsaPrivateKey((String) futuData.get("rsaPrivateKey"));
        }
        
        // IB配置
        Map<String, Object> ibData = (Map<String, Object>) data.get("ib");
        if (ibData != null) {
            ibConfig = new IBConfig();
            ibConfig.setPort((Integer) ibData.getOrDefault("port", 7497));
            ibConfig.setClientId((Integer) ibData.getOrDefault("clientId", 1));
            ibConfig.setServerVersion((Integer) ibData.getOrDefault("serverVersion", 178));
        }
        
        if (futuConfig == null) {
            futuConfig = createDefaultFutuConfig();
        }
        if (ibConfig == null) {
            ibConfig = createDefaultIBConfig();
        }
    }
    
    private void loadDefaults() {
        futuConfig = createDefaultFutuConfig();
        ibConfig = createDefaultIBConfig();
    }
    
    private FutuConfig createDefaultFutuConfig() {
        FutuConfig config = new FutuConfig();
        config.setHost("127.0.0.1");
        config.setPort(11111);
        // 注意：實際使用時需要配置真實的密碼和密鑰
        return config;
    }
    
    private IBConfig createDefaultIBConfig() {
        IBConfig config = new IBConfig();
        config.setPort(7497);  // TWS默認端口
        config.setClientId(1);
        config.setServerVersion(178);  // 最新IB API版本
        return config;
    }
    
    // Getters
    public FutuConfig getFutuConfig() { return futuConfig; }
    public IBConfig getIbConfig() { return ibConfig; }
    
    /**
     * 富途API配置
     */
    public static class FutuConfig {
        private String host;
        private int port;
        private String unlockPassword;
        private String rsaPrivateKey;
        
        // Getters and Setters
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        
        public String getUnlockPassword() { return unlockPassword; }
        public void setUnlockPassword(String unlockPassword) { this.unlockPassword = unlockPassword; }
        
        public String getRsaPrivateKey() { return rsaPrivateKey; }
        public void setRsaPrivateKey(String rsaPrivateKey) { this.rsaPrivateKey = rsaPrivateKey; }
    }
    
    /**
     * IB Gateway模擬器配置
     */
    public static class IBConfig {
        private int port;
        private int clientId;
        private int serverVersion;
        
        // Getters and Setters
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        
        public int getClientId() { return clientId; }
        public void setClientId(int clientId) { this.clientId = clientId; }
        
        public int getServerVersion() { return serverVersion; }
        public void setServerVersion(int serverVersion) { this.serverVersion = serverVersion; }
    }
}
