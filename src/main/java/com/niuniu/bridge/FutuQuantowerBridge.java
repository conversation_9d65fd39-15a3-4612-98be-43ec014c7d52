package com.niuniu.bridge;

import com.niuniu.bridge.config.BridgeConfig;
import com.niuniu.bridge.futu.FutuApiClient;
import com.niuniu.bridge.ib.IBGatewaySimulator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 富途-Quantower橋接器主程序
 * 
 * 功能：
 * 1. 模擬IB Gateway，監聽7497端口
 * 2. 連接富途OpenAPI獲取市場數據
 * 3. 將富途數據轉換為IB協議格式
 * 4. 為Quantower提供無縫的數據服務
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FutuQuantowerBridge {
    
    private static final Logger logger = LoggerFactory.getLogger(FutuQuantowerBridge.class);
    
    private final BridgeConfig config;
    private final FutuApiClient futuClient;
    private final IBGatewaySimulator ibSimulator;
    
    public FutuQuantowerBridge() {
        this.config = BridgeConfig.load();
        this.futuClient = new FutuApiClient(config.getFutuConfig());
        this.ibSimulator = new IBGatewaySimulator(config.getIbConfig(), futuClient);
    }
    
    /**
     * 啟動橋接服務
     */
    public void start() {
        logger.info("=== 富途-Quantower橋接器啟動 ===");
        logger.info("版本: {}", getClass().getPackage().getImplementationVersion());
        
        try {
            // 1. 連接富途API
            logger.info("正在連接富途OpenAPI...");
            futuClient.connect();
            logger.info("富途API連接成功");
            
            // 2. 啟動IB Gateway模擬器
            logger.info("正在啟動IB Gateway模擬器...");
            ibSimulator.start();
            logger.info("IB Gateway模擬器啟動成功，監聽端口: {}", config.getIbConfig().getPort());
            
            // 3. 註冊關閉鉤子
            Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
            
            logger.info("=== 橋接器啟動完成，等待Quantower連接 ===");
            
            // 保持主線程運行
            Thread.currentThread().join();
            
        } catch (Exception e) {
            logger.error("橋接器啟動失敗", e);
            shutdown();
            System.exit(1);
        }
    }
    
    /**
     * 關閉橋接服務
     */
    public void shutdown() {
        logger.info("正在關閉橋接器...");
        
        try {
            if (ibSimulator != null) {
                ibSimulator.shutdown();
                logger.info("IB Gateway模擬器已關閉");
            }
            
            if (futuClient != null) {
                futuClient.disconnect();
                logger.info("富途API連接已關閉");
            }
            
        } catch (Exception e) {
            logger.error("關閉橋接器時發生錯誤", e);
        }
        
        logger.info("橋接器已完全關閉");
    }
    
    /**
     * 主程序入口
     */
    public static void main(String[] args) {
        // 設置系統屬性
        System.setProperty("java.net.useSystemProxies", "true");
        
        // 打印啟動信息
        printBanner();
        
        // 啟動橋接器
        FutuQuantowerBridge bridge = new FutuQuantowerBridge();
        bridge.start();
    }
    
    /**
     * 打印啟動橫幅
     */
    private static void printBanner() {
        System.out.println();
        System.out.println("  ███████╗██╗   ██╗████████╗██╗   ██╗");
        System.out.println("  ██╔════╝██║   ██║╚══██╔══╝██║   ██║");
        System.out.println("  █████╗  ██║   ██║   ██║   ██║   ██║");
        System.out.println("  ██╔══╝  ██║   ██║   ██║   ██║   ██║");
        System.out.println("  ██║     ╚██████╔╝   ██║   ╚██████╔╝");
        System.out.println("  ╚═╝      ╚═════╝    ╚═╝    ╚═════╝ ");
        System.out.println();
        System.out.println("  ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗████████╗ ██████╗ ██╗    ██╗███████╗██████╗ ");
        System.out.println("  ██╔═══██╗██║   ██║██╔══██╗████╗  ██║╚══██╔══╝██╔═══██╗██║    ██║██╔════╝██╔══██╗");
        System.out.println("  ██║   ██║██║   ██║███████║██╔██╗ ██║   ██║   ██║   ██║██║ █╗ ██║█████╗  ██████╔╝");
        System.out.println("  ██║▄▄ ██║██║   ██║██╔══██║██║╚██╗██║   ██║   ██║   ██║██║███╗██║██╔══╝  ██╔══██╗");
        System.out.println("  ╚██████╔╝╚██████╔╝██║  ██║██║ ╚████║   ██║   ╚██████╔╝╚███╔███╔╝███████╗██║  ██║");
        System.out.println("   ╚══▀▀═╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝    ╚═════╝  ╚══╝╚══╝ ╚══════╝╚═╝  ╚═╝");
        System.out.println();
        System.out.println("  富途 ←→ Quantower 數據橋接器");
        System.out.println("  版本: 1.0.0 | 作者: NIUNIU Team");
        System.out.println();
    }
}
