package com.niuniu.bridge.ib;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * IB協議幀解碼器
 * 
 * IB協議格式：
 * [4字節長度][消息內容]
 * 
 * 消息內容是以\0分隔的文本字段
 */
public class IBFrameDecoder extends ByteToMessageDecoder {
    
    private static final Logger logger = LoggerFactory.getLogger(IBFrameDecoder.class);
    private static final int MAX_FRAME_LENGTH = 1024 * 1024; // 1MB最大幀長度
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 檢查是否有足夠的字節來讀取長度字段
        if (in.readableBytes() < 4) {
            return; // 等待更多數據
        }
        
        // 標記當前讀取位置
        in.markReaderIndex();
        
        // 讀取消息長度（大端序）
        int messageLength = in.readInt();
        
        // 驗證消息長度
        if (messageLength < 0 || messageLength > MAX_FRAME_LENGTH) {
            logger.error("無效的消息長度: {}", messageLength);
            ctx.close();
            return;
        }
        
        // 檢查是否有足夠的字節來讀取完整消息
        if (in.readableBytes() < messageLength) {
            // 重置讀取位置，等待更多數據
            in.resetReaderIndex();
            return;
        }
        
        // 讀取消息內容
        byte[] messageBytes = new byte[messageLength];
        in.readBytes(messageBytes);
        
        // 轉換為字符串
        String message = new String(messageBytes, StandardCharsets.UTF_8);
        
        // 解析字段（以\0分隔）
        String[] fields = message.split("\0");
        
        if (logger.isDebugEnabled()) {
            logger.debug("收到IB消息: 長度={}, 字段數={}, 內容={}", 
                messageLength, fields.length, String.join(",", fields));
        }
        
        // 創建IB消息對象
        IBMessage ibMessage = new IBMessage(fields);
        out.add(ibMessage);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("IB幀解碼錯誤", cause);
        ctx.close();
    }
}
