package com.niuniu.bridge.ib;

import com.niuniu.bridge.config.BridgeConfig;
import com.niuniu.bridge.futu.FutuApiClient;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * IB Gateway模擬器
 * 
 * 功能：
 * 1. 監聽指定端口，等待Quantower連接
 * 2. 實現IB協議握手和認證
 * 3. 處理市場數據請求並轉發給富途API
 * 4. 將富途數據轉換為IB格式返回
 */
public class IBGatewaySimulator {
    
    private static final Logger logger = LoggerFactory.getLogger(IBGatewaySimulator.class);
    
    private final BridgeConfig.IBConfig config;
    private final FutuApiClient futuClient;
    private final AtomicInteger nextReqId = new AtomicInteger(1);
    private final ConcurrentHashMap<Integer, IBClientSession> sessions = new ConcurrentHashMap<>();
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;
    
    public IBGatewaySimulator(BridgeConfig.IBConfig config, FutuApiClient futuClient) {
        this.config = config;
        this.futuClient = futuClient;
    }
    
    /**
     * 啟動IB Gateway模擬器
     */
    public void start() throws InterruptedException {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 128)
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                .childOption(ChannelOption.TCP_NODELAY, true)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ChannelPipeline pipeline = ch.pipeline();
                        
                        // 添加IB協議處理器
                        pipeline.addLast("frameDecoder", new IBFrameDecoder());
                        pipeline.addLast("frameEncoder", new IBFrameEncoder());
                        pipeline.addLast("protocolHandler", new IBProtocolHandler(
                            IBGatewaySimulator.this, futuClient));
                    }
                });
            
            // 綁定端口並啟動服務器
            ChannelFuture future = bootstrap.bind(config.getPort()).sync();
            serverChannel = future.channel();
            
            logger.info("IB Gateway模擬器已啟動，監聽端口: {}", config.getPort());
            logger.info("等待Quantower連接...");
            
        } catch (Exception e) {
            logger.error("啟動IB Gateway模擬器失敗", e);
            shutdown();
            throw e;
        }
    }
    
    /**
     * 關閉模擬器
     */
    public void shutdown() {
        logger.info("正在關閉IB Gateway模擬器...");
        
        try {
            // 關閉所有客戶端會話
            sessions.values().forEach(IBClientSession::close);
            sessions.clear();
            
            // 關閉服務器通道
            if (serverChannel != null) {
                serverChannel.close().sync();
            }
            
        } catch (Exception e) {
            logger.error("關閉服務器通道時發生錯誤", e);
        } finally {
            // 關閉事件循環組
            if (workerGroup != null) {
                workerGroup.shutdownGracefully();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully();
            }
        }
        
        logger.info("IB Gateway模擬器已關閉");
    }
    
    /**
     * 註冊新的客戶端會話
     */
    public void registerSession(IBClientSession session) {
        int sessionId = nextReqId.getAndIncrement();
        session.setSessionId(sessionId);
        sessions.put(sessionId, session);
        
        logger.info("新客戶端連接: {} (會話ID: {})", 
            session.getRemoteAddress(), sessionId);
    }
    
    /**
     * 移除客戶端會話
     */
    public void removeSession(IBClientSession session) {
        sessions.remove(session.getSessionId());
        logger.info("客戶端斷開連接: {} (會話ID: {})", 
            session.getRemoteAddress(), session.getSessionId());
    }
    
    /**
     * 獲取下一個請求ID
     */
    public int getNextReqId() {
        return nextReqId.getAndIncrement();
    }
    
    /**
     * 獲取配置
     */
    public BridgeConfig.IBConfig getConfig() {
        return config;
    }
    
    /**
     * 獲取活躍會話數量
     */
    public int getActiveSessionCount() {
        return sessions.size();
    }
    
    /**
     * 廣播消息給所有客戶端
     */
    public void broadcast(String message) {
        sessions.values().forEach(session -> {
            try {
                session.sendMessage(message);
            } catch (Exception e) {
                logger.error("廣播消息失敗: {}", session.getRemoteAddress(), e);
            }
        });
    }
}
