package com.niuniu.bridge.ib;

import java.util.Arrays;

/**
 * IB協議消息
 * 
 * 封裝IB協議的消息字段
 */
public class IBMessage {
    
    private final String[] fields;
    
    public IBMessage(String[] fields) {
        this.fields = fields != null ? fields : new String[0];
    }
    
    public IBMessage(String... fields) {
        this.fields = fields != null ? fields : new String[0];
    }
    
    /**
     * 獲取所有字段
     */
    public String[] getFields() {
        return fields;
    }
    
    /**
     * 獲取字段數量
     */
    public int getFieldCount() {
        return fields.length;
    }
    
    /**
     * 獲取指定索引的字段
     */
    public String getField(int index) {
        if (index >= 0 && index < fields.length) {
            return fields[index];
        }
        return "";
    }
    
    /**
     * 獲取指定索引的字段作為整數
     */
    public int getFieldAsInt(int index) {
        String field = getField(index);
        if (field.isEmpty()) {
            return 0;
        }
        try {
            return Integer.parseInt(field);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 獲取指定索引的字段作為雙精度浮點數
     */
    public double getFieldAsDouble(int index) {
        String field = getField(index);
        if (field.isEmpty()) {
            return 0.0;
        }
        try {
            return Double.parseDouble(field);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }
    
    /**
     * 獲取消息ID（第一個字段）
     */
    public int getMessageId() {
        return getFieldAsInt(0);
    }
    
    /**
     * 檢查是否為握手消息
     */
    public boolean isHandshakeMessage() {
        return fields.length > 0 && "API".equals(fields[0]);
    }
    
    /**
     * 檢查是否為版本消息
     */
    public boolean isVersionMessage() {
        return fields.length >= 2 && fields[0].startsWith("v");
    }
    
    @Override
    public String toString() {
        return "IBMessage{" +
                "fields=" + Arrays.toString(fields) +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IBMessage ibMessage = (IBMessage) o;
        return Arrays.equals(fields, ibMessage.fields);
    }
    
    @Override
    public int hashCode() {
        return Arrays.hashCode(fields);
    }
}
