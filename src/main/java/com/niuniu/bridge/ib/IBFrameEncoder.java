package com.niuniu.bridge.ib;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * IB協議幀編碼器
 * 
 * 將IBMessage編碼為IB協議格式：
 * [4字節長度][消息內容]
 */
public class IBFrameEncoder extends MessageToByteEncoder<IBMessage> {
    
    private static final Logger logger = LoggerFactory.getLogger(IBFrameEncoder.class);
    
    @Override
    protected void encode(ChannelHandlerContext ctx, IBMessage msg, ByteBuf out) throws Exception {
        // 構建消息內容（字段以\0分隔）
        StringBuilder messageBuilder = new StringBuilder();
        String[] fields = msg.getFields();
        
        for (int i = 0; i < fields.length; i++) {
            messageBuilder.append(fields[i]);
            if (i < fields.length - 1) {
                messageBuilder.append('\0');
            }
        }
        
        // 轉換為字節數組
        byte[] messageBytes = messageBuilder.toString().getBytes(StandardCharsets.UTF_8);
        
        // 寫入長度字段（4字節，大端序）
        out.writeInt(messageBytes.length);
        
        // 寫入消息內容
        out.writeBytes(messageBytes);
        
        if (logger.isDebugEnabled()) {
            logger.debug("發送IB消息: 長度={}, 字段數={}, 內容={}", 
                messageBytes.length, fields.length, String.join(",", fields));
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("IB幀編碼錯誤", cause);
        ctx.close();
    }
}
