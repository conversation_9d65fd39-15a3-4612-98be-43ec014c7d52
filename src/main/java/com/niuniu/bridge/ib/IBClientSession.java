package com.niuniu.bridge.ib;

import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketAddress;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * IB客戶端會話
 * 
 * 管理單個Quantower客戶端的連接狀態和數據訂閱
 */
public class IBClientSession {
    
    private static final Logger logger = LoggerFactory.getLogger(IBClientSession.class);
    
    private final Channel channel;
    private final IBGatewaySimulator simulator;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final ConcurrentHashMap<Integer, String> marketDataSubscriptions = new ConcurrentHashMap<>();
    
    private int sessionId;
    private int clientId;
    private String accountName = "DU123456"; // 默認賬戶名
    
    public IBClientSession(Channel channel, IBGatewaySimulator simulator) {
        this.channel = channel;
        this.simulator = simulator;
    }
    
    /**
     * 發送消息到客戶端
     */
    public void sendMessage(String message) {
        if (channel.isActive()) {
            // 將字符串消息轉換為IBMessage
            String[] fields = message.split("\0");
            IBMessage ibMessage = new IBMessage(fields);
            channel.writeAndFlush(ibMessage);
        } else {
            logger.warn("嘗試向非活躍通道發送消息: {}", message);
        }
    }
    
    /**
     * 發送IBMessage到客戶端
     */
    public void sendMessage(IBMessage message) {
        if (channel.isActive()) {
            channel.writeAndFlush(message);
        } else {
            logger.warn("嘗試向非活躍通道發送IBMessage");
        }
    }
    
    /**
     * 訂閱市場數據
     */
    public void subscribeMarketData(int reqId, String symbol) {
        marketDataSubscriptions.put(reqId, symbol);
        logger.debug("訂閱市場數據: reqId={}, symbol={}", reqId, symbol);
    }
    
    /**
     * 取消市場數據訂閱
     */
    public void unsubscribeMarketData(int reqId) {
        String symbol = marketDataSubscriptions.remove(reqId);
        if (symbol != null) {
            logger.debug("取消市場數據訂閱: reqId={}, symbol={}", reqId, symbol);
        }
    }
    
    /**
     * 獲取所有訂閱的市場數據
     */
    public ConcurrentHashMap<Integer, String> getMarketDataSubscriptions() {
        return new ConcurrentHashMap<>(marketDataSubscriptions);
    }
    
    /**
     * 關閉會話
     */
    public void close() {
        connected.set(false);
        marketDataSubscriptions.clear();
        
        if (channel.isActive()) {
            channel.close();
        }
        
        logger.info("客戶端會話已關閉: sessionId={}", sessionId);
    }
    
    /**
     * 檢查會話是否活躍
     */
    public boolean isActive() {
        return connected.get() && channel.isActive();
    }
    
    /**
     * 獲取遠程地址
     */
    public SocketAddress getRemoteAddress() {
        return channel.remoteAddress();
    }
    
    // Getters and Setters
    public int getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(int sessionId) {
        this.sessionId = sessionId;
    }
    
    public int getClientId() {
        return clientId;
    }
    
    public void setClientId(int clientId) {
        this.clientId = clientId;
    }
    
    public String getAccountName() {
        return accountName;
    }
    
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
    
    public boolean isConnected() {
        return connected.get();
    }
    
    public void setConnected(boolean connected) {
        this.connected.set(connected);
    }
    
    public Channel getChannel() {
        return channel;
    }
    
    @Override
    public String toString() {
        return "IBClientSession{" +
                "sessionId=" + sessionId +
                ", clientId=" + clientId +
                ", accountName='" + accountName + '\'' +
                ", connected=" + connected.get() +
                ", remoteAddress=" + getRemoteAddress() +
                ", subscriptions=" + marketDataSubscriptions.size() +
                '}';
    }
}
