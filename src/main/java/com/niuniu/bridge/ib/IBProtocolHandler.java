package com.niuniu.bridge.ib;

import com.niuniu.bridge.data.DataForwarder;
import com.niuniu.bridge.data.HistoricalDataHandler;
import com.niuniu.bridge.futu.FutuApiClient;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * IB協議處理器
 * 
 * 負責處理來自Quantower的IB協議消息，並轉換為富途API調用
 */
public class IBProtocolHandler extends SimpleChannelInboundHandler<IBMessage> {
    
    private static final Logger logger = LoggerFactory.getLogger(IBProtocolHandler.class);
    
    private final IBGatewaySimulator simulator;
    private final FutuApiClient futuClient;
    private final DataForwarder dataForwarder;
    private final HistoricalDataHandler historicalDataHandler;
    private IBClientSession session;

    public IBProtocolHandler(IBGatewaySimulator simulator, FutuApiClient futuClient,
                           DataForwarder dataForwarder, HistoricalDataHandler historicalDataHandler) {
        this.simulator = simulator;
        this.futuClient = futuClient;
        this.dataForwarder = dataForwarder;
        this.historicalDataHandler = historicalDataHandler;
    }
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 創建客戶端會話
        session = new IBClientSession(ctx.channel(), simulator);
        simulator.registerSession(session);
        
        logger.info("Quantower客戶端連接: {}", ctx.channel().remoteAddress());
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (session != null) {
            simulator.removeSession(session);
            session = null;
        }
        logger.info("Quantower客戶端斷開: {}", ctx.channel().remoteAddress());
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, IBMessage msg) throws Exception {
        try {
            if (msg.isHandshakeMessage()) {
                handleHandshake(ctx, msg);
            } else if (msg.isVersionMessage()) {
                handleVersionNegotiation(ctx, msg);
            } else {
                handleIBMessage(ctx, msg);
            }
        } catch (Exception e) {
            logger.error("處理IB消息失敗: {}", msg, e);
            // 發送錯誤響應
            sendError(ctx, -1, 502, "消息處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 處理握手消息
     */
    private void handleHandshake(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到握手消息: {}", msg);
        
        // IB握手響應：發送服務器版本和連接時間
        String serverVersion = String.valueOf(simulator.getConfig().getServerVersion());
        String connectionTime = String.valueOf(System.currentTimeMillis() / 1000);
        
        IBMessage response = new IBMessage(serverVersion, connectionTime);
        ctx.writeAndFlush(response);
        
        logger.info("發送握手響應: 版本={}, 時間={}", serverVersion, connectionTime);
    }
    
    /**
     * 處理版本協商
     */
    private void handleVersionNegotiation(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到版本協商: {}", msg);
        
        // 解析客戶端版本範圍
        String versionInfo = msg.getField(0);
        // 格式: "v157..178" 或類似
        
        // 設置會話為已連接狀態
        if (session != null) {
            session.setConnected(true);
            
            // 發送初始化消息
            sendInitialMessages(ctx);
        }
    }
    
    /**
     * 發送初始化消息
     */
    private void sendInitialMessages(ChannelHandlerContext ctx) {
        // 1. 發送nextValidId (消息ID: 9)
        IBMessage nextValidId = new IBMessage("9", "1", "1000");
        ctx.writeAndFlush(nextValidId);
        
        // 2. 發送managedAccounts (消息ID: 15)
        IBMessage managedAccounts = new IBMessage("15", "1", "DU123456");
        ctx.writeAndFlush(managedAccounts);
        
        logger.info("發送初始化消息完成");
    }
    
    /**
     * 處理IB業務消息
     */
    private void handleIBMessage(ChannelHandlerContext ctx, IBMessage msg) {
        int messageId = msg.getMessageId();
        
        switch (messageId) {
            case 1:  // reqMktData
                handleMarketDataRequest(ctx, msg);
                break;
            case 2:  // cancelMktData
                handleCancelMarketData(ctx, msg);
                break;
            case 20: // reqHistoricalData
                handleHistoricalDataRequest(ctx, msg);
                break;
            case 3:  // placeOrder
                handlePlaceOrder(ctx, msg);
                break;
            case 71: // startApi
                handleStartApi(ctx, msg);
                break;
            default:
                logger.debug("未處理的消息類型: {}", messageId);
                break;
        }
    }
    
    /**
     * 處理市場數據請求
     */
    private void handleMarketDataRequest(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到市場數據請求: {}", msg);

        // 解析請求參數
        int reqId = msg.getFieldAsInt(2);
        String symbol = msg.getField(4);  // 股票代碼
        String secType = msg.getField(5); // 證券類型
        String exchange = msg.getField(8); // 交易所
        String currency = msg.getField(9); // 貨幣

        logger.info("請求市場數據: reqId={}, symbol={}, secType={}, exchange={}, currency={}",
            reqId, symbol, secType, exchange, currency);

        // 使用數據轉發器處理訂閱
        if (dataForwarder != null && session != null) {
            dataForwarder.addMarketDataSubscription(reqId, symbol, secType, exchange, currency, session);
            session.subscribeMarketData(reqId, symbol);
        } else {
            // 發送錯誤
            sendError(ctx, reqId, 200, "數據轉發器未初始化");
        }
    }
    
    /**
     * 發送模擬市場數據
     */
    private void sendMockMarketData(ChannelHandlerContext ctx, int reqId, String symbol) {
        // 發送bid價格 (tickType=1)
        IBMessage bidPrice = new IBMessage("1", "1", String.valueOf(reqId), "1", "100.50", "100");
        ctx.writeAndFlush(bidPrice);
        
        // 發送ask價格 (tickType=2)
        IBMessage askPrice = new IBMessage("1", "1", String.valueOf(reqId), "2", "100.55", "200");
        ctx.writeAndFlush(askPrice);
        
        // 發送最新價格 (tickType=4)
        IBMessage lastPrice = new IBMessage("1", "1", String.valueOf(reqId), "4", "100.52", "150");
        ctx.writeAndFlush(lastPrice);
        
        logger.info("發送模擬市場數據: symbol={}, reqId={}", symbol, reqId);
    }
    
    /**
     * 處理取消市場數據
     */
    private void handleCancelMarketData(ChannelHandlerContext ctx, IBMessage msg) {
        int reqId = msg.getFieldAsInt(2);
        logger.info("取消市場數據: reqId={}", reqId);

        // 使用數據轉發器取消訂閱
        if (dataForwarder != null) {
            dataForwarder.removeMarketDataSubscription(reqId);
        }

        if (session != null) {
            session.unsubscribeMarketData(reqId);
        }
    }
    
    /**
     * 處理歷史數據請求
     */
    private void handleHistoricalDataRequest(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到歷史數據請求: {}", msg);

        // 使用歷史數據處理器處理請求
        if (historicalDataHandler != null && session != null) {
            historicalDataHandler.handleHistoricalDataRequest(session, msg);
        } else {
            int reqId = msg.getFieldAsInt(2);
            sendError(ctx, reqId, 162, "歷史數據處理器未初始化");
        }
    }
    
    /**
     * 處理下單請求
     */
    private void handlePlaceOrder(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到下單請求: {}", msg);
        // TODO: 實現下單邏輯
    }
    
    /**
     * 處理API啟動
     */
    private void handleStartApi(ChannelHandlerContext ctx, IBMessage msg) {
        logger.info("收到API啟動請求: {}", msg);
        // 通常在這裡發送賬戶信息等
    }
    
    /**
     * 發送錯誤消息
     */
    private void sendError(ChannelHandlerContext ctx, int reqId, int errorCode, String errorMsg) {
        IBMessage error = new IBMessage("4", "2", String.valueOf(reqId), 
            String.valueOf(errorCode), errorMsg);
        ctx.writeAndFlush(error);
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("協議處理器異常", cause);
        ctx.close();
    }
}
