# 富途-Quantower橋接器配置文件

# 富途OpenAPI配置
futu:
  # 富途OpenAPI服務器地址
  host: 127.0.0.1
  # 富途OpenAPI端口（默認11111）
  port: 11111
  # 交易解鎖密碼（如果需要交易功能）
  unlockPassword: ""
  # RSA私鑰路徑（如果使用加密連接）
  rsaPrivateKey: ""

# IB Gateway模擬器配置
ib:
  # 監聽端口（Quantower連接此端口）
  port: 7497
  # 客戶端ID
  clientId: 1
  # 服務器版本（IB API版本）
  serverVersion: 178

# 日誌配置
logging:
  level:
    com.niuniu.bridge: DEBUG
    com.futu.openapi: INFO
    io.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/futu-quantower-bridge.log
    max-size: 100MB
    max-history: 30

# 數據轉換配置
data:
  # 價格精度
  pricePrecision: 4
  # 數量精度
  quantityPrecision: 0
  # 數據緩存時間（秒）
  cacheTimeout: 30
  # 最大訂閱數量
  maxSubscriptions: 100

# 性能配置
performance:
  # Netty工作線程數
  workerThreads: 4
  # 消息處理線程池大小
  messageThreadPoolSize: 8
  # 連接超時時間（毫秒）
  connectionTimeout: 30000
  # 心跳間隔（秒）
  heartbeatInterval: 30
