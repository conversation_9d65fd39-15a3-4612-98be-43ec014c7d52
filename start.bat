@echo off
chcp 65001 > nul
title 富途-Quantower橋接器

echo.
echo ===============================================
echo   富途-Quantower橋接器啟動腳本
echo ===============================================
echo.

:: 檢查Java環境
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 未找到Java環境，請確保已安裝Java 11或更高版本
    pause
    exit /b 1
)

:: 檢查Maven環境
mvn -version > nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 未找到Maven環境，請確保已安裝Maven
    pause
    exit /b 1
)

:: 創建日誌目錄
if not exist "logs" mkdir logs

:: 編譯項目
echo [信息] 正在編譯項目...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo [錯誤] 項目編譯失敗
    pause
    exit /b 1
)

:: 啟動橋接器
echo [信息] 正在啟動富途-Quantower橋接器...
echo [信息] 請確保富途牛牛客戶端已啟動並開啟OpenAPI
echo [信息] 橋接器將監聽端口7497，等待Quantower連接
echo.
echo [提示] 如需測試連接，請運行: mvn exec:java -Dexec.mainClass="com.niuniu.bridge.test.IBConnectionTest"
echo.

mvn exec:java -Dexec.mainClass="com.niuniu.bridge.FutuQuantowerBridge" -Dexec.args="%*"

echo.
echo [信息] 橋接器已停止
pause
