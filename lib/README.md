# 依賴庫目錄

## 富途OpenAPI SDK安裝說明

由於富途OpenAPI Java SDK不在Maven中央倉庫中，需要手動下載並安裝。

### 下載SDK

1. 訪問富途開發者中心：https://openapi.futunn.com/
2. 註冊開發者賬號
3. 下載Java SDK

### 安裝步驟

1. **將SDK jar文件放置到此目錄**
   ```
   lib/futu-api.jar
   ```

2. **或者安裝到本地Maven倉庫**
   ```bash
   mvn install:install-file \
     -Dfile=futu-api.jar \
     -DgroupId=com.futu \
     -DartifactId=futu-api \
     -Dversion=6.0.1308 \
     -Dpackaging=jar
   ```

### 驗證安裝

運行以下命令驗證SDK是否正確安裝：
```bash
mvn compile
```

如果編譯成功，說明SDK已正確安裝。

### 注意事項

- 請確保下載的SDK版本與pom.xml中指定的版本一致
- SDK文件名必須為 `futu-api.jar`
- 如果使用不同版本，請相應修改pom.xml中的版本號
