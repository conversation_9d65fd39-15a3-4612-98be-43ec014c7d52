# 富途-Quantower橋接器

## 項目簡介

富途-Quantower橋接器是一個創新的金融數據橋接解決方案，通過模擬Interactive Brokers (IB) Gateway的方式，將富途牛牛的股票數據無縫集成到Quantower交易平台中。

### 核心功能

- 🔗 **無縫集成**：Quantower認為連接的是真正的IB Gateway
- 📊 **實時數據**：支持港股、美股、A股實時行情
- 📈 **歷史K線**：提供各種時間週期的歷史數據
- 💹 **交易功能**：支持通過Quantower直接下單交易
- 🛡️ **穩定可靠**：基於成熟的IB協議標準

### 技術架構

```
富途OpenAPI → Java橋接器 → IB協議模擬 → Quantower平台
     ↓              ↓              ↓           ↓
  實時數據      協議轉換        Socket服務    原生顯示
  歷史K線      消息封裝        端口7497      圖表分析
  交易功能      數據轉換        IB格式       訂單管理
```

## 快速開始

### 環境要求

- **Java**: JDK 11 或更高版本
- **Maven**: 3.6 或更高版本
- **富途牛牛**: 已安裝並開啟OpenAPI功能
- **Quantower**: 已安裝交易平台

### 安裝步驟

1. **克隆項目**
   ```bash
   git clone <repository-url>
   cd NIUNIUQT
   ```

2. **配置富途API**
   編輯 `src/main/resources/application.yml`：
   ```yaml
   futu:
     host: 127.0.0.1
     port: 11111
     unlockPassword: "your_password"  # 如需交易功能
   ```

3. **編譯項目**
   ```bash
   mvn clean compile
   ```

4. **啟動橋接器**
   ```bash
   # Windows
   start.bat

   # Linux/Mac
   ./start.sh
   ```

5. **測試連接**
   ```bash
   # Windows
   test.bat

   # Linux/Mac
   ./test.sh
   ```

### 連接Quantower

1. 啟動橋接器後，它會監聽端口 `7497`
2. 在Quantower中添加新連接：
   - 選擇 "Interactive Brokers"
   - 服務器地址：`127.0.0.1`
   - 端口：`7497`
   - 客戶端ID：`1`

### 使用示例

#### 查看港股數據
1. 在Quantower中搜索股票：`700` (騰訊)
2. 橋接器會自動轉換為富途格式並獲取數據
3. 實時價格會顯示在Quantower圖表中

#### 查看美股數據
1. 在Quantower中搜索股票：`AAPL` (蘋果)
2. 選擇交易所：`SMART` 或 `NASDAQ`
3. 查看實時行情和歷史K線

#### 查看歷史數據
1. 右鍵點擊圖表選擇時間週期
2. 橋接器會從富途API獲取對應的K線數據
3. 支持1分鐘到月線的各種週期

## 配置說明

### 富途API配置

```yaml
futu:
  host: 127.0.0.1          # 富途OpenAPI地址
  port: 11111              # 富途OpenAPI端口
  unlockPassword: ""       # 交易解鎖密碼
  rsaPrivateKey: ""        # RSA私鑰路徑
```

### IB模擬器配置

```yaml
ib:
  port: 7497              # 監聽端口
  clientId: 1             # 客戶端ID
  serverVersion: 178      # IB API版本
```

### 日誌配置

日誌文件位置：
- 主日誌：`logs/futu-quantower-bridge.log`
- 錯誤日誌：`logs/error.log`

## 支持的功能

### ✅ 已實現

- [x] IB協議握手和認證
- [x] TCP Socket服務器
- [x] 富途API連接
- [x] 基礎消息框架
- [x] 配置管理系統
- [x] 日誌記錄
- [x] **實時市場數據轉發**
- [x] **歷史K線數據**
- [x] **數據格式轉換**
- [x] **股票代碼映射**
- [x] **連接測試工具**

### 🚧 開發中

- [ ] 訂單管理和交易功能
- [ ] 賬戶信息同步
- [ ] 多市場支持優化

### 📋 計劃中

- [ ] 多賬戶支持
- [ ] 數據緩存優化
- [ ] 性能監控
- [ ] Web管理界面

## 故障排除

### 常見問題

1. **連接失敗**
   - 檢查富途牛牛是否已啟動
   - 確認OpenAPI功能已開啟
   - 驗證端口7497未被占用

2. **數據不更新**
   - 檢查富途API連接狀態
   - 確認股票代碼格式正確
   - 查看日誌文件中的錯誤信息

3. **Quantower無法連接**
   - 確認橋接器已成功啟動
   - 檢查防火牆設置
   - 驗證Quantower的IB連接配置

### 日誌分析

查看詳細日誌：
```bash
tail -f logs/futu-quantower-bridge.log
```

## 開發指南

### 項目結構

```
src/main/java/com/niuniu/bridge/
├── FutuQuantowerBridge.java    # 主程序入口
├── config/                     # 配置管理
│   └── BridgeConfig.java
├── ib/                        # IB協議模擬
│   ├── IBGatewaySimulator.java
│   ├── IBProtocolHandler.java
│   ├── IBFrameDecoder.java
│   ├── IBFrameEncoder.java
│   ├── IBMessage.java
│   └── IBClientSession.java
└── futu/                      # 富途API集成
    ├── FutuApiClient.java
    ├── FutuQotHandler.java
    └── FutuTrdHandler.java
```

### 擴展開發

1. **添加新的數據類型**
   - 在 `FutuQotHandler` 中添加處理邏輯
   - 在 `IBProtocolHandler` 中實現IB格式轉換

2. **支持新的交易功能**
   - 擴展 `FutuTrdHandler` 處理交易回調
   - 在 `IBProtocolHandler` 中實現訂單管理

## 許可證

本項目採用 MIT 許可證 - 詳見 [LICENSE](LICENSE) 文件

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 聯繫方式

- 項目主頁：[GitHub Repository]
- 技術支持：[Support Email]
- 文檔：[Documentation]

---

**免責聲明**：本軟件僅供學習和研究使用，使用者需自行承擔交易風險。
